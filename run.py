#!/usr/bin/env python3
"""
Simple launcher for SPY Options Trading System.
Works on both Windows PC and Google Colab.

Usage:
  python run.py --train
  python run.py --optimize --n_trials 25
  python run.py --eval_only
  python run.py --signal
"""

import os
import sys
import subprocess
import platform

def is_colab():
    """Check if running in Google Colab."""
    return any([
        "COLAB_RELEASE_TAG" in os.environ,
        "COLAB_GPU" in os.environ,
        os.path.exists('/content')
    ])

def find_spy_script():
    """Find the SPY script file."""
    possible_names = ["SPY-Aroon UP.py", "SPY-aroon down.py", "SPY.py"]
    for name in possible_names:
        if os.path.exists(name):
            return name
    return "SPY-Aroon UP.py"  # Default

def main():
    """Main launcher function."""
    # Detect environment
    env_type = "Colab" if is_colab() else f"Windows PC" if platform.system() == "Windows" else "Local"
    print(f"🚀 SPY Trading System - Running on {env_type}")
    
    # Find SPY script
    spy_script = find_spy_script()
    if not os.path.exists(spy_script):
        print(f"❌ Error: {spy_script} not found!")
        return 1
    
    # Build command
    cmd = [sys.executable, spy_script] + sys.argv[1:]
    
    print(f"📄 Script: {spy_script}")
    print(f"🏃 Command: {' '.join(cmd)}")
    print("-" * 50)
    
    # Run the command
    try:
        result = subprocess.run(cmd)
        return result.returncode
    except KeyboardInterrupt:
        print("\n⚠️  Interrupted by user")
        return 130
    except Exception as e:
        print(f"❌ Error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())