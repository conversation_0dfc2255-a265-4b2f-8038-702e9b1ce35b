#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
validation_agent.py

A two-agent system for validating RL trading signals with an LLM expert,
grounded in <PERSON>'s ("Trader Vic") reversal framework, and augmented
with curated macroeconomic indicators for SPY.

Additions vs your drafts:
- Keeps robust swing-based feature engineering (peaks/troughs).
- Uses practical yet faithful approximations to <PERSON><PERSON><PERSON><PERSON>’s trendline + 1-2-3.
- Enhanced 2B pattern: adds amplitude/strength.
- Curated macro indicators via FMP stable/economic-indicators (empirically relevant for SPY).

"""

import os
import re
import io
import json
import math
import time
import logging
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any

import requests
import numpy as np
import pandas as pd
import yfinance as yf

pd.options.mode.chained_assignment = None  # silence chained assignment warnings
pd.set_option('future.no_silent_downcasting', True)  # suppress downcasting warnings

# --------------------------- Logging -----------------------------------------
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s"
)
log = logging.getLogger("validation_agent")

# ---------------------- Config and Constants ---------------------------------

# LLM
LLM_MODEL = "openai/gpt-oss-20b:free"  
OPENROUTER_API_KEY = "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"

# FMP API
FMP_API_KEY = os.environ.get("FMP_API_KEY", "WNo9hSsEWW6kqLXIfiXeIgluMusBt6N5")
FMP_BASE_URL = "https://financialmodelingprep.com/stable/economic-indicators"

# Output file (Windows path as requested)
OUTPUT_FILE = Path(r"C:\Users\<USER>\Desktop\Execute agent\trading_analysis_output.txt")

# Curated macro indicators strongly tied to SPY directionality
# (balance growth vs inflation/labor/consumption)
CURATED_INDICATORS = [
    "inflationRate",                    # YoY inflation rate (higher = bearish via tighter policy)
    "CPI",                              # CPI index level / trend
    "federalFunds",                     # Policy rate (higher = bearish equity multiple compression)
    "unemploymentRate",                 # Higher = bearish
    "initialClaims",                    # Higher = labor stress = bearish
    "totalNonfarmPayroll",              # Strength = bullish risk appetite
    "retailSales",                      # Consumption strength = bullish
    "durableGoods",                     # Capex proxy = bullish when rising
    "industrialProductionTotalIndex",   # Real economy strength = bullish
    "realGDP",                          # Growth = bullish
    "consumerSentiment"                 # Forward spending risk = bullish
]

# Directional signs for macro -> SPY
# +1 means higher readings are bullish for equities; -1 means higher is bearish
INDICATOR_DIRECTIONS = {
    "inflationRate": -1.0,
    "CPI": -1.0,
    "federalFunds": -1.0,
    "unemploymentRate": -1.0,
    "initialClaims": -1.0,
    "totalNonfarmPayroll": +1.0,
    "retailSales": +1.0,
    "durableGoods": +1.0,
    "industrialProductionTotalIndex": +1.0,
    "realGDP": +1.0,
    "consumerSentiment": +1.0,
}

# Relative weights by empirical importance
INDICATOR_WEIGHTS = {
    "inflationRate": 1.25,
    "CPI": 0.75,
    "federalFunds": 1.25,
    "unemploymentRate": 1.0,
    "initialClaims": 0.75,
    "totalNonfarmPayroll": 1.0,
    "retailSales": 0.75,
    "durableGoods": 0.5,
    "industrialProductionTotalIndex": 0.5,
    "realGDP": 0.75,
    "consumerSentiment": 0.5,
}

# ----------------------- Output Helper ---------------------------------------

class TeeBuffer:
    """
    Collects all lines of output while also echoing to console.
    """
    def __init__(self):
        self.buf = io.StringIO()

    def write(self, s: str = ""):
        print(s)
        self.buf.write(s + ("\n" if not s.endswith("\n") else ""))

    def getvalue(self) -> str:
        return self.buf.getvalue()

    def close(self):
        self.buf.close()


def write_output_file(text: str, path: Path = OUTPUT_FILE):
    try:
        path.parent.mkdir(parents=True, exist_ok=True)
        with open(path, "w", encoding="utf-8") as f:
            f.write(text)
        log.info("Full output written to: %s", str(path))
    except Exception as e:
        log.error("Failed to write output file to %s: %s", str(path), e)


# --------------------- Utility Functions -------------------------------------

def read_current_position(file_path: str = "my_position.txt") -> Optional[Dict[str, Any]]:
    """
    Reads the trader's current position from a simple text file.
    Returns a dictionary with position details or None if in cash.

    Expected file format (key: value per line), e.g.:
      type: CALL
      strike: 520
      expiry: 2025-09-20
      qty: 1
    """
    if not os.path.exists(file_path):
        log.info("'my_position.txt' not found. Assuming you are in cash.")
        return None

    try:
        with open(file_path, "r") as f:
            lines = [ln.strip() for ln in f if ln.strip()]

        if not lines:
            log.info("'my_position.txt' is empty. Assuming you are in cash.")
            return None

        position: Dict[str, Any] = {}
        for line in lines:
            if ":" in line:
                key, value = [x.strip() for x in line.split(":", 1)]
                position[key.lower()] = value

        if not all(k in position for k in ["type", "strike", "expiry"]):
            raise ValueError("Position file malformed. Must contain 'type', 'strike', and 'expiry'.")

        position["type"] = position["type"].upper()
        if position["type"] not in ["CALL", "PUT"]:
            raise ValueError("Position 'type' must be either 'CALL' or 'PUT'.")

        position["strike"] = float(position["strike"])
        # Parse/validate date
        try:
            datetime.strptime(position["expiry"], "%Y-%m-%d")
        except Exception:
            log.warning("Expiry format is not YYYY-MM-DD; continuing anyway.")

        if "qty" in position:
            try:
                position["qty"] = int(position["qty"])
            except Exception:
                position["qty"] = 1

        return position

    except Exception as e:
        log.error("Could not read or parse 'my_position.txt'. Error: %s", e)
        raise


def get_market_data(ticker: str, days: int = 200) -> pd.DataFrame:
    """
    Fetches the last N trading days of market data for the given ticker.
    """
    log.info("Fetching last %d trading days of data for %s...", days, ticker)
    end_date = datetime.now()
    start_date = end_date - timedelta(days=int(days * 2.2))
    data = yf.download(
        ticker,
        start=start_date.strftime("%Y-%m-%d"),
        end=end_date.strftime("%Y-%m-%d"),
        progress=False,
        auto_adjust=False,
        ignore_tz=True,
        interval="1d",
    )
    if data.empty:
        raise ValueError(f"Failed to fetch data for {ticker}")
    
    # Handle MultiIndex columns if present
    if isinstance(data.columns, pd.MultiIndex):
        data.columns = data.columns.droplevel(1)
    
    data = data.dropna()
    data = data.tail(days).copy()
    log.info("Data fetched: %d rows.", len(data))
    return data


# --------------------- Sperandeo Feature Engineering -------------------------

def _local_extrema_flags(df: pd.DataFrame) -> Tuple[pd.Series, pd.Series]:
    """
    Basic local maxima/minima on a one-bar neighborhood.
    """
    is_peak = (df["High"] > df["High"].shift(1)) & (df["High"] > df["High"].shift(-1))
    is_trough = (df["Low"] < df["Low"].shift(1)) & (df["Low"] < df["Low"].shift(-1))
    return is_peak.fillna(False), is_trough.fillna(False)


def identify_trend(df: pd.DataFrame) -> pd.DataFrame:
    """
    Identifies trend regime using swing structure:
    - Uptrend: higher highs and higher lows
    - Downtrend: lower highs and lower lows
    - Else: sideways (0)
    """
    df = df.copy()
    is_peak, is_trough = _local_extrema_flags(df)

    peaks = df["High"].where(is_peak)
    troughs = df["Low"].where(is_trough)

    last_peak = peaks.ffill()
    prev_peak = peaks.ffill().shift(1)
    last_trough = troughs.ffill()
    prev_trough = troughs.ffill().shift(1)

    is_uptrend = (last_peak > prev_peak) & (last_trough > prev_trough)
    is_downtrend = (last_peak < prev_peak) & (last_trough < prev_trough)

    df["trend_state"] = 0
    df.loc[is_uptrend, "trend_state"] = 1
    df.loc[is_downtrend, "trend_state"] = -1
    return df


def apply_123_rule(df: pd.DataFrame, use_trendline_condition: bool = True) -> pd.DataFrame:
    """
    Applies Sperandeo's 1-2-3 Rule, tracking "armed" and "triggered" states.

    1-2-3 Top:
      1) Break of uptrend line (if use_trendline_condition=True, require trendline_break=+1)
      2) Lower high forms
      3) Price breaks below last swing low -> trigger

    1-2-3 Bottom: symmetric.

    Output:
      123_reversal_state:
        +1.0: Top Triggered
        +0.5: Top Armed (lower high formed after condition 1)
        -1.0: Bottom Triggered
        -0.5: Bottom Armed (higher low formed after condition 1)
    """
    df = df.copy()
    is_peak, is_trough = _local_extrema_flags(df)

    peak_vals = df["High"].where(is_peak)
    trough_vals = df["Low"].where(is_trough)

    last_peak = peak_vals.ffill()
    last_trough = trough_vals.ffill()

    # 2) Lower high formed? (vs previous peak)
    prev_peak_at_peaks = peak_vals.shift(1)
    lower_high_now = is_peak & (df["High"] < prev_peak_at_peaks)

    # 2) Higher low formed? (vs previous trough)
    prev_trough_at_troughs = trough_vals.shift(1)
    higher_low_now = is_trough & (df["Low"] > prev_trough_at_troughs)

    # Optional: enforce Condition 1 via trendline breaks (from detect_trendline_break)
    if use_trendline_condition and "trendline_break" in df.columns:
        cond1_top = df["trendline_break"].fillna(0).astype(int) == 1     # bearish break of uptrend line
        cond1_bot = df["trendline_break"].fillna(0).astype(int) == -1    # bullish break of downtrend line
    else:
        # If not using trendline condition, allow arms to form without it
        cond1_top = pd.Series(True, index=df.index)
        cond1_bot = pd.Series(True, index=df.index)

    # Persist "armed" state forward after Condition 1 occurs
    df["top_armed"] = (cond1_top & lower_high_now).replace(False, np.nan).ffill().fillna(False).infer_objects(copy=False)
    df["bottom_armed"] = (cond1_bot & higher_low_now).replace(False, np.nan).ffill().fillna(False).infer_objects(copy=False)

    # 3) Trigger when price breaches last swing low (top) or last swing high (bottom)
    break_below_last_trough = df["Close"] < last_trough
    break_above_last_peak = df["Close"] > last_peak

    top_triggered = df["top_armed"] & break_below_last_trough
    bottom_triggered = df["bottom_armed"] & break_above_last_peak

    state = np.zeros(len(df))
    state[df["top_armed"]] = 0.5
    state[df["bottom_armed"]] = -0.5
    state[top_triggered] = 1.0
    state[bottom_triggered] = -1.0

    df["123_reversal_state"] = state

    # Clean up helper columns
    df.drop(columns=["top_armed", "bottom_armed"], inplace=True)
    return df


def apply_2b_rule(df: pd.DataFrame, lookback: int = 5) -> pd.DataFrame:
    """
    Applies Sperandeo's 2B rule (failed breakout/breakdown) with a configurable lookback.
    Adds 2B amplitude (strength) based on overshoot relative to the reference swing level.

    Output:
      2b_signal:
        +1: 2B Top (failed breakout) -> Bearish
        -1: 2B Bottom (failed breakdown) -> Bullish
         0: none
      2b_strength: overshoot/undershoot magnitude (0..)
    """
    df = df.copy()
    is_peak, is_trough = _local_extrema_flags(df)

    last_peak = df["High"].where(is_peak).ffill()
    last_trough = df["Low"].where(is_trough).ffill()

    # Breakout above last peak in recent window, then close back below that peak level -> 2B Top
    breakout = df["High"] > last_peak.shift(1)
    breakout_recent = breakout.rolling(window=lookback, min_periods=1).max().astype(bool)
    failed_breakout = df["Close"] < last_peak.shift(1)
    top_2b = breakout_recent & failed_breakout

    # Breakdown below last trough in recent window, then close back above that trough -> 2B Bottom
    breakdown = df["Low"] < last_trough.shift(1)
    breakdown_recent = breakdown.rolling(window=lookback, min_periods=1).max().astype(bool)
    failed_breakdown = df["Close"] > last_trough.shift(1)
    bottom_2b = breakdown_recent & failed_breakdown

    # De-duplicate contiguous True spans (flag only first bar of a new event)
    top_trigger = top_2b & (~top_2b.shift(1).fillna(False).infer_objects(copy=False))
    bottom_trigger = bottom_2b & (~bottom_2b.shift(1).fillna(False).infer_objects(copy=False))

    sig = np.zeros(len(df), dtype=int)
    sig[top_trigger] = 1
    sig[bottom_trigger] = -1
    df["2b_signal"] = sig

    # 2B amplitude (strength): max overshoot/undershoot in the lookback window prior to trigger
    strength = np.zeros(len(df))
    for i in np.where(top_trigger)[0]:
        ref = float(last_peak.shift(1).iloc[i]) if not np.isnan(last_peak.shift(1).iloc[i]) else np.nan
        if not np.isnan(ref) and ref != 0:
            lo = max(0, i - lookback + 1)
            overshoot = (df["High"].iloc[lo:i+1].max() - ref) / abs(ref)
            strength[i] = max(0.0, overshoot)
    for i in np.where(bottom_trigger)[0]:
        ref = float(last_trough.shift(1).iloc[i]) if not np.isnan(last_trough.shift(1).iloc[i]) else np.nan
        if not np.isnan(ref) and ref != 0:
            lo = max(0, i - lookback + 1)
            undershoot = (ref - df["Low"].iloc[lo:i+1].min()) / abs(ref)
            strength[i] = max(0.0, undershoot)
    df["2b_strength"] = strength

    return df


def quantify_consecutive_days(df: pd.DataFrame) -> pd.DataFrame:
    """
    Computes signed streak length of consecutive up/down closes.
    Positive values: up-day streak length; Negative values: down-day streak length.
    """
    df = df.copy()
    price_change = df["Close"].diff()
    sign = np.sign(price_change).fillna(0)
    grp = (sign != sign.shift()).cumsum()
    counts = sign.groupby(grp).cumcount() + 1
    df["consecutive_days"] = counts * sign
    return df


def apply_four_day_rule(df: pd.DataFrame) -> pd.DataFrame:
    """
    Implements the spirit of Sperandeo's Four-Day Rule:
      - After 4+ consecutive up days, the first down day is a potential bearish reversal day.
      - After 4+ consecutive down days, the first up day is a potential bullish reversal day.

    Output:
      four_day_signal:
        +1: bearish reversal day after >=4 up days
        -1: bullish reversal day after >=4 down days
         0: none
    """
    df = df.copy()
    df = quantify_consecutive_days(df)
    price_change = df["Close"].diff()
    sign = np.sign(price_change).fillna(0)

    prev_streak = df["consecutive_days"].shift(1).fillna(0)
    current_sign = sign

    bearish_reversal = (prev_streak >= 4) & (current_sign < 0)
    bullish_reversal = (prev_streak <= -4) & (current_sign > 0)

    sig = np.zeros(len(df), dtype=int)
    sig[bearish_reversal] = 1
    sig[bullish_reversal] = -1
    df["four_day_signal"] = sig
    return df


def detect_trendline_break(df: pd.DataFrame) -> pd.DataFrame:
    """
    Approximates trendline breaks using the last two swing lows (uptrend) or highs (downtrend).
    - For uptrend line: connect last two swing lows; if Close crosses below the extrapolated line -> bearish break (+1).
    - For downtrend line: connect last two swing highs; if Close crosses above the extrapolated line -> bullish break (-1).

    Output:
      trendline_break:
        +1: bearish break of uptrend line
        -1: bullish break of downtrend line
         0: none
      trendline_slope_up, trendline_slope_down: slopes for context (NaN if not defined)
    """
    df = df.copy()
    is_peak, is_trough = _local_extrema_flags(df)

    n = len(df)
    tl_break = np.zeros(n, dtype=int)
    slope_up = np.full(n, np.nan)
    slope_down = np.full(n, np.nan)

    lows: List[Tuple[int, float]] = []
    highs: List[Tuple[int, float]] = []

    last_up_cross = False
    last_down_cross = False

    closes = df["Close"].values
    lows_arr = df["Low"].values
    highs_arr = df["High"].values

    for i in range(n):
        if is_trough.iloc[i]:
            lows.append((i, lows_arr[i]))
            if len(lows) > 2:
                lows.pop(0)
        if is_peak.iloc[i]:
            highs.append((i, highs_arr[i]))
            if len(highs) > 2:
                highs.pop(0)

        # Uptrend line from two recent swing lows
        if len(lows) == 2:
            (x1, y1), (x2, y2) = lows[0], lows[1]
            if x2 != x1:
                m_up = (y2 - y1) / (x2 - x1)
                slope_up[i] = m_up
                y_line = y1 + m_up * (i - x1)
                crossed = closes[i] < y_line
                if crossed and not last_up_cross and i >= x2:
                    tl_break[i] = 1  # bearish break
                last_up_cross = crossed

        # Downtrend line from two recent swing highs
        if len(highs) == 2:
            (x1h, y1h), (x2h, y2h) = highs[0], highs[1]
            if x2h != x1h:
                m_dn = (y2h - y1h) / (x2h - x1h)
                slope_down[i] = m_dn
                y_line_dn = y1h + m_dn * (i - x1h)
                crossed_dn = closes[i] > y_line_dn
                if crossed_dn and not last_down_cross and i >= x2h:
                    tl_break[i] = -1  # bullish break
                last_down_cross = crossed_dn

    df["trendline_break"] = tl_break
    df["trendline_slope_up"] = slope_up
    df["trendline_slope_down"] = slope_down
    return df


def generate_sperandeo_features(df: pd.DataFrame) -> pd.DataFrame:
    """
    Master function to compute all Sperandeo-related features.
    """
    features = df.copy()
    features = identify_trend(features)
    features = detect_trendline_break(features)
    features = apply_123_rule(features, use_trendline_condition=True)
    features = apply_2b_rule(features, lookback=5)
    features = apply_four_day_rule(features)
    return features


# ------------------------ Macro: FMP Integration -----------------------------




def fetch_economic_indicators(names: List[str],
                              start_date: Optional[datetime] = None,
                              end_date: Optional[datetime] = None) -> pd.DataFrame:
    """
    Fetch economic indicators from FMP 'stable/economic-indicators' endpoint.
    Fetches indicators one by one to avoid batch call issues.

    Returns a long DataFrame with columns: ['name', 'date', 'value'].
    """
    start_str = start_date.strftime("%Y-%m-%d") if start_date else None
    end_str = end_date.strftime("%Y-%m-%d") if end_date else None

    data_items: List[dict] = []

    # Fetch each indicator individually to avoid batch call warnings
    for nm in names:
        params = {"name": nm, "apikey": FMP_API_KEY}
        if start_str:
            params["from"] = start_str
        if end_str:
            params["to"] = end_str
        try:
            log.info("Fetching indicator: %s", nm)
            resp = requests.get(FMP_BASE_URL, params=params, timeout=30)
            resp.raise_for_status()
            arr = resp.json()
            if isinstance(arr, list):
                for it in arr:
                    if isinstance(it, dict):
                        name_val = it.get("name", nm)
                        date_val = it.get("date")
                        val = it.get("value")
                        if date_val is not None and val is not None:
                            data_items.append({"name": name_val, "date": date_val, "value": val})
            time.sleep(0.2)  # Small delay between requests to be respectful to API
        except Exception as e:
            log.warning("Failed to fetch indicator %s: %s", nm, e)
            time.sleep(0.5)

    if not data_items:
        log.warning("No economic indicator data fetched.")
        return pd.DataFrame(columns=["name", "date", "value"])

    df = pd.DataFrame(data_items)
    df["date"] = pd.to_datetime(df["date"], errors="coerce")
    df["value"] = pd.to_numeric(df["value"], errors="coerce")
    df = df.dropna(subset=["date", "value"])
    return df.sort_values(["name", "date"]).reset_index(drop=True)


def _bounded_tanh(x: float) -> float:
    # Conservative scaling
    return math.tanh(x / 2.0)


def compute_macro_scores(df_macro: pd.DataFrame,
                         directions: Dict[str, float],
                         weights: Dict[str, float]) -> Tuple[Dict[str, Dict[str, Any]], float, str]:
    """
    For each indicator, compute:
      - last value, prev value, delta
      - z-score of last value vs trailing window (prefer 12, fallback as needed)
      - signed score = direction * tanh(z/2)

    Return:
      - per-indicator summary dict
      - macro_bias_score in [-1, +1]
      - macro_bias_label ("Bullish", "Bearish", "Neutral")
    """
    indicator_summary: Dict[str, Dict[str, Any]] = {}
    total_weight = 0.0
    weighted_sum = 0.0

    if df_macro.empty:
        return indicator_summary, 0.0, "Neutral"

    for nm, g in df_macro.groupby("name"):
        series = g.sort_values("date")["value"].astype(float).tail(36)
        if len(series) < 3:
            continue

        last_val = float(series.iloc[-1])
        prev_val = float(series.iloc[-2])
        delta = last_val - prev_val

        window = min(12, max(3, len(series)))
        m = float(series.tail(window).mean())
        s = float(series.tail(window).std(ddof=0)) if window > 1 else 0.0
        z = 0.0 if s == 0.0 else (last_val - m) / (s + 1e-8)

        direction = directions.get(nm, 0.0)
        score = direction * _bounded_tanh(z)  # in [-1, 1]

        weight = weights.get(nm, 0.5)
        total_weight += weight
        weighted_sum += weight * score

        indicator_summary[nm] = {
            "last": last_val,
            "prev": prev_val,
            "delta": delta,
            "z": z,
            "direction": direction,
            "score": score,
            "weight": weight,
        }

    macro_bias_score = weighted_sum / total_weight if total_weight > 0 else 0.0
    if macro_bias_score >= 0.2:
        macro_label = "Bullish"
    elif macro_bias_score <= -0.2:
        macro_label = "Bearish"
    else:
        macro_label = "Neutral"

    return indicator_summary, macro_bias_score, macro_label


def macro_summary_text(indicator_summary: Dict[str, Dict[str, Any]],
                       macro_bias_score: float,
                       macro_label: str) -> str:
    """
    Compose a compact textual summary of macro indicators for the LLM prompt.
    """
    if not indicator_summary:
        return "No macroeconomic data available."

    lines = []
    lines.append(f"Macro Bias: {macro_label} (score={macro_bias_score:.2f}, -1 bearish to +1 bullish)")
    lines.append("Latest indicators:")
    items = sorted(indicator_summary.items(), key=lambda kv: kv[1].get("weight", 0.0), reverse=True)
    for nm, d in items:
        last = d.get("last")
        prev = d.get("prev")
        delta = d.get("delta")
        z = d.get("z")
        score = d.get("score")
        arrow = "↑" if (delta or 0) > 0 else ("↓" if (delta or 0) < 0 else "→")
        lines.append(f"- {nm}: {last:.4g} ({arrow} Δ={delta:.4g}, z={z:.2f}, score={score:.2f})")
    return "\n".join(lines)


# ------------------------ LLM Prompt and Call --------------------------------

def construct_prompt(rl_signal: Dict[str, Any],
                     sperandeo_features: pd.DataFrame,
                     current_position: Optional[Dict[str, Any]],
                     macro_text: str,
                     raw_spy_data: pd.DataFrame) -> str:
    """
    Construct the detailed prompt for the LLM.
    """
    latest = sperandeo_features.iloc[-1]

    position_str = "Currently in cash (no open position)."
    if current_position:
        position_str = (
            f"Currently HOLDING a {current_position['type']} option with:\n"
            f"- Strike Price: {current_position['strike']}\n"
            f"- Expiration Date: {current_position['expiry']}\n"
            f"- Quantity: {current_position.get('qty', 1)}"
        )

    # Include 2B strength in prompt
    twob_strength = latest.get("2b_strength", 0.0)

    # Prepare full SPY data for LLM analysis (complete 220-day dataset used for Sperandeo features)
    # Leveraging the model's 128k context window for comprehensive historical analysis
    spy_data_str = raw_spy_data[['Open', 'High', 'Low', 'Close', 'Volume']].round(2).to_string()

    prompt = f"""<persona>
You are Victor Sperandeo ("Trader Vic"), the legendary trader and author of "Trader Vic—Methods of a Wall Street Master." Your analysis is disciplined, methodical, and grounded in your specific rules for identifying trend reversals.
</persona>

<task>
Your job is to perform an independent analysis of raw SPY price data using your signature methodology, compare your findings against a set of pre-computed indicators, and synthesize all available information to validate a Reinforcement Learning (RL) agent's trading signal.
</task>

<methodology>
At the heart of your approach is disciplined trend change confirmation using these specific technical indicators:

TREND LINE ANALYSIS: You employ objective trend line drawing:
- Uptrend: Line from lowest low to highest minor low preceding the highest high (line must not pass through prices between points)
- Downtrend: Line from highest high to lowest minor high before the lowest low
- Breaking of this trend line is the initial signal of potential trend change

THE 1-2-3 RULE (Your signature three-step reversal framework):
- Step 1: Break of the established trend line
- Step 2: Test of the high/low - price attempts to retest recent high (uptrend) or low (downtrend) but fails to establish new one
- Step 3: Move beyond previous minor price point - price falls below previous minor low (uptrend) or rallies above previous minor high (downtrend)
- ALL THREE conditions must be fulfilled to confirm trend reversal

THE 2B PATTERN (Your most heavily weighted criterion):
- Price moves beyond previous high (uptrend) or low (downtrend) but fails to sustain breakout and quickly reverses
- This "false breakout" indicates momentum loss and is your strongest indicator of trend change
- You weigh this pattern more heavily than any other single criterion

THE FOUR-DAY RULE (Market climax identification):
- Four consecutive days moving in trend direction, followed by a single day reversal
- Suggests high probability of trend change within an intermediate trend.
</methodology>

<context_data>
<rl_signal_to_validate>
- Decision: {rl_signal.get('decision_type', 'N/A')}
- Confidence: {rl_signal.get('confidence_score', 'N/A')}
- Details: {json.dumps(rl_signal.get('details')) if rl_signal.get('details') else 'N/A'}

IMPORTANT: When the RL signal shows "HOLD", this means maintain the current position - whether that is holding cash (no position) OR holding an existing option position. Do NOT interpret "HOLD" as only referring to cash positions.
</rl_signal_to_validate>

<current_position>
{position_str}
</current_position>

<primary_data_for_analysis>
<description>220-Day Raw SPY Data</description>
<data>{spy_data_str}</data>
</primary_data_for_analysis>

<reference_for_comparison>
<description>Pre-Computed Indicators from the last trading day</description>
<indicators>
- Trend State: {latest['trend_state']:.1f} (1: Uptrend, -1: Downtrend, 0: Sideways)
- 1-2-3 Reversal: {latest['123_reversal_state']:.1f} (1: Top Triggered, 0.5: Top Armed, -1: Bottom Triggered, -0.5: Bottom Armed)
- 2B Pattern: {latest['2b_signal']:.1f} (1: 2B Top/Bearish, -1: 2B Bottom/Bullish)
- 2B Strength: {twob_strength:.3f} (Magnitude of the false breakout)
- Four-Day Rule: {latest.get('four_day_signal', 0):.1f} (1: Bearish, -1: Bullish)
- Trendline Break: {latest.get('trendline_break', 0):.1f} (+1: Bearish break, -1: Bullish break)
- Consecutive Days: {latest['consecutive_days']:.1f}
</indicators>
</reference_for_comparison>

<macroeconomic_context>
{macro_text}
</macroeconomic_context>
</context_data>

<instructions>
Follow this three-step validation process:

<step_1 name="Independent Technical Analysis">
Based *only* on the <primary_data_for_analysis>, perform your signature analysis. In your reasoning, describe what you see regarding:
- Trend Assessment: What is the current primary trend, and has its trendline been broken?
- 1-2-3 Rule: Is a 1-2-3 reversal pattern forming or has one been confirmed?
- 2B Pattern: Do you see evidence of a recent 2B top or bottom? This is your most important criterion.
</step_1>

<step_2 name="Comparative Analysis">
Now, compare your findings from Step 1 to the <reference_for_comparison>.
- Note any direct confirmations where the pre-computed indicators match your independent analysis.
- Highlight any significant discrepancies.
</step_2>

<step_3 name="Synthesis and Final Validation">
Synthesize all three sources: your independent analysis (Step 1), the pre-computed indicators (Step 2), and the macroeconomic context.
- Determine Market Bias: Based on the synthesis, state your overall market bias: "Bullish," "Bearish," or "Neutral."
- Validate the RL Signal: Does your final, synthesized bias support or contradict the RL agent's decision?
- Assign Agreement Score: Based on the strength of your conviction, rate your agreement with the RL signal on a scale of 1.0 to 10.0.
</step_3>
</instructions>

<output_format>
You MUST respond in valid JSON with the following structure:
{{
  "market_bias": "...",
  "reasoning": "...",
  "agreement_score": X.X
}}
</output_format>alysis of all three components (1.0-10.0, 0.5 intervals)

Respond in your characteristic disciplined, methodical style. Be specific about what you see in the charts and why.

Output Format - You MUST respond in valid JSON:
{{
  "market_bias": "...",
  "reasoning": "...",
  "agreement_score": X.X
}}
"""
    return prompt


def extract_json_str(s: str) -> Optional[str]:
    """
    Given an LLM response string, attempt to extract a JSON object.
    Handles code fences and extra text, including incomplete JSON.
    """
    s = s.strip()
    # Remove markdown fences if present
    if s.startswith("```json"):
        s = s[7:]
    if s.endswith("```"):
        s = s[:-3]
    s = s.strip()

    # If the entire string is JSON
    if s.startswith("{") and s.endswith("}"):
        return s

    # Try to find the first {...} block
    match = re.search(r"\{.*\}", s, re.DOTALL)
    if match:
        return match.group(0)
    
    # Handle incomplete JSON - try to find opening brace and attempt to close it
    if s.startswith("{"):
        # Try to find where the JSON might end based on common patterns
        # Look for the last complete field before truncation
        lines = s.split('\n')
        json_lines = []
        for line in lines:
            json_lines.append(line)
            # If we see a truncated line or incomplete field, try to close the JSON
            if line.strip().endswith('"') and not line.strip().endswith('",'):
                # This might be an incomplete string value, try to close it
                json_lines.append('"}')
                break
        
        potential_json = '\n'.join(json_lines)
        if not potential_json.endswith('}'):
            potential_json += '}'
        
        # Test if this creates valid JSON
        try:
            json.loads(potential_json)
            return potential_json
        except:
            pass
    
    return None


def get_llm_analysis(rl_signal: Dict[str, Any],
                     sperandeo_features: pd.DataFrame,
                     current_position: Optional[Dict[str, Any]],
                     macro_text: str,
                     raw_spy_data: pd.DataFrame) -> Optional[Dict[str, Any]]:
    """
    Calls the OpenRouter API to get the LLM's state-aware analysis.
    """
    if not OPENROUTER_API_KEY:
        log.error("OPENROUTER_API_KEY environment variable not set.")
        return None

    log.info("Contacting LLM (%s) for validation...", LLM_MODEL)
    prompt = construct_prompt(rl_signal, sperandeo_features, current_position, macro_text, raw_spy_data)

    # Retry logic with exponential backoff for rate limits
    max_retries = 3
    base_delay = 2
    
    for attempt in range(max_retries):
        try:
            if attempt > 0:
                delay = base_delay * (2 ** (attempt - 1))
                log.info("Retrying LLM request in %d seconds (attempt %d/%d)...", delay, attempt + 1, max_retries)
                time.sleep(delay)
            
            response = requests.post(
                url="https://openrouter.ai/api/v1/chat/completions",
                headers={
                    "Authorization": f"Bearer {OPENROUTER_API_KEY}",
                    "Content-Type": "application/json"
                },
                data=json.dumps({
                    "model": LLM_MODEL,
                    "messages": [{"role": "user", "content": prompt}],
                    "temperature": 0,
                    "max_tokens": 10000,
                }),
                timeout=60
            )
            if response.status_code == 429:  # Rate limit
                if attempt < max_retries - 1:  # Not the last attempt
                    continue  # Retry
                else:
                    log.error("Rate limit exceeded after %d attempts", max_retries)
                    return None
            
            response.raise_for_status()
            
            # Debug: Log the raw response
            log.info("Raw API response status: %d", response.status_code)
            log.info("Raw API response headers: %s", dict(response.headers))
            log.info("Raw API response text (first 500 chars): %s", response.text[:500])
            
            result = response.json()
            llm_output_str = result["choices"][0]["message"]["content"]

            log.info("LLM response received. Parsing JSON...")
            log.info("Raw LLM Output:\n---\n%s\n---", llm_output_str)
            
            json_str = extract_json_str(llm_output_str)
            if not json_str:
                # Try to create a fallback response
                log.warning("No JSON found, creating fallback response")
                return {
                    "market_bias": "Neutral",
                    "reasoning": "LLM response could not be parsed properly. Using neutral stance.",
                    "agreement_score": 5.0
                }

            llm_analysis = json.loads(json_str)
            if not all(k in llm_analysis for k in ["market_bias", "reasoning", "agreement_score"]):
                raise ValueError("LLM response missing required keys (market_bias, reasoning, agreement_score).")

            log.info("LLM analysis parsed successfully.")
            return llm_analysis

        except requests.exceptions.RequestException as e:
            if attempt < max_retries - 1:  # Not the last attempt
                log.warning("API request failed (attempt %d/%d): %s", attempt + 1, max_retries, e)
                continue  # Retry
            else:
                log.error("API request failed after %d attempts: %s", max_retries, e)
                return None
        except (json.JSONDecodeError, KeyError, ValueError) as e:
            log.error("Failed to parse LLM response: %s", e)
            log.error("Raw LLM Output:\n---\n%s\n---", locals().get("llm_output_str", ""))
            # Return fallback response instead of None
            return {
                "market_bias": "Neutral",
                "reasoning": "LLM response parsing failed. Using neutral stance for safety.",
                "agreement_score": 5.0
            }
    
    # If all retries failed
    return {
        "market_bias": "Neutral",
        "reasoning": "LLM request failed after multiple attempts. Using neutral stance for safety.",
        "agreement_score": 5.0
    }


# ------------------------------ Main Flow ------------------------------------

def main():
    out = TeeBuffer()

    try:
        # --- 0. Read the Trader's Current Position ---
        current_position = read_current_position()
        out.write("--- 0. Current Trader Position ---")
        if current_position:
            out.write(json.dumps(current_position, indent=2))
        else:
            out.write("In Cash")

        # --- 1. Get the RL Agent's Signal ---
        signal_file = "trading_signal_v3.1.json"
        if not os.path.exists(signal_file):
            out.write(f"\nERROR: Signal file '{signal_file}' not found.")
            out.write("Please run your RL generator (e.g., python SPY.py --signal) to create it.")
            write_output_file(out.getvalue())
            return

        with open(signal_file, "r") as f:
            rl_signal = json.load(f)

        out.write("\n--- 1. RL Agent Signal ---")
        out.write(json.dumps(rl_signal, indent=2))

        # --- 2. Market Data & Sperandeo Features ---
        spy_data = get_market_data("SPY", days=220)  # ample bars for swing analysis
        sperandeo_features = generate_sperandeo_features(spy_data)

        out.write("\n--- 2. Sperandeo Feature Analysis (Latest Day) ---")
        out.write(sperandeo_features.tail(1).to_string())

        # --- 3. Macro Data (FMP) ---
        out.write("\n--- 3. Fetching Macroeconomic Indicators (FMP) ---")
        end = datetime.now()
        start = end - timedelta(days=90)  # Last 90 days window (dynamic date setting)
        df_macro = fetch_economic_indicators(CURATED_INDICATORS, start, end)

        ind_summary, macro_score, macro_label = compute_macro_scores(
            df_macro, INDICATOR_DIRECTIONS, INDICATOR_WEIGHTS
        )
        macro_text = macro_summary_text(ind_summary, macro_score, macro_label)

        out.write("\nMacro Summary:")
        out.write(macro_text)

        # --- 4. LLM Validation ---
        llm_analysis = get_llm_analysis(rl_signal, sperandeo_features, current_position, macro_text, spy_data)

        if not llm_analysis:
            out.write("\nCould not get a valid analysis from the LLM. Aborting.")
            write_output_file(out.getvalue())
            return

        out.write("\n--- 4. LLM Validation Agent Analysis ---")
        out.write(json.dumps(llm_analysis, indent=2))

        # --- 5. Final Decision Logic ---
        out.write("\n--- 5. Final Trade Decision ---")
        agreement_score = float(llm_analysis.get("agreement_score", 0.0))
        rl_decision = str(rl_signal.get("decision_type", "ERROR")).upper()
        llm_bias = str(llm_analysis.get("market_bias", "Unknown")).lower()

        # Conflict heuristic between RL decision and LLM bias:
        is_conflict = False
        if ("CALL" in rl_decision or "BUY_CALL" in rl_decision) and "bearish" in llm_bias:
            is_conflict = True
        if ("PUT" in rl_decision or "BUY_PUT" in rl_decision) and "bullish" in llm_bias:
            is_conflict = True
        if rl_decision == "HOLD" and "neutral" not in llm_bias:
            # If HOLD but LLM is not neutral, potential conflict
            is_conflict = True

        out.write(f"Analysis: RL Signal='{rl_decision}', LLM Bias='{llm_bias.title()}', Conflict={is_conflict}, Score={agreement_score:.2f}")

        if is_conflict and agreement_score < 6.0:
            out.write("\nFINAL DECISION: DO NOT EXECUTE.")
            out.write("Reason: Conflict between agents and agreement score is below 6.0.")
        elif agreement_score >= 6.0:
            out.write("\nFINAL DECISION: EXECUTE.")
            out.write(f"Reason: Agreement score of {agreement_score:.1f} is high, validating the RL signal.")
        else:
            out.write("\nFINAL DECISION: DO NOT EXECUTE.")
            out.write(f"Reason: Agreement score of {agreement_score:.1f} is below the execution threshold of 6.0.")

    except Exception as e:
        out.write(f"\nAn unexpected error occurred: {e}")

    # Always attempt to write the captured output to the requested file
    write_output_file(out.getvalue())
    out.close()


if __name__ == "__main__":
    main()