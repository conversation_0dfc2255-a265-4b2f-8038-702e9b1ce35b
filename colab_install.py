#!/usr/bin/env python3
"""
Simple wrapper for install_dependencies.py optimized for Google Colab usage.
This script automatically detects Colab environment and uses sensible defaults.
"""

import sys
import os
import subprocess

def main():
    # Check if we're in Colab
    is_colab = (
        "COLAB_RELEASE_TAG" in os.environ
        or "COLAB_GPU" in os.environ
        or any("google.colab" in str(module) for module in sys.modules.keys())
    )
    
    if not is_colab:
        print("[INFO] Not running in Google Colab. Using install_dependencies.py directly might be better.")
    
    # Default arguments for Colab
    args = [
        sys.executable, 
        "install_dependencies.py",
        "--mode", "prod",  # Production dependencies only
        "--cuda", "cpu",   # CPU mode by default (user can override)
    ]
    
    # Pass through any additional arguments
    if len(sys.argv) > 1:
        args.extend(sys.argv[1:])
    
    print("[COLAB] Installing SPY Options Trading dependencies...")
    print(f"[COLAB] Command: {' '.join(args[1:])}")
    print("[COLAB] This may take a few minutes...")
    
    # Run the main installer
    try:
        result = subprocess.run(args, check=False)
        if result.returncode == 0:
            print("\n[COLAB] ✅ Installation completed successfully!")
            print("[COLAB] You can now run your main.py or other trading scripts.")
        else:
            print(f"\n[COLAB] ❌ Installation failed with code {result.returncode}")
            print("[COLAB] Check the output above for error details.")
        return result.returncode
    except Exception as e:
        print(f"[COLAB] ❌ Error running installer: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())