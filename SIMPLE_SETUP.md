# SPY Options Trading - Simple Setup

## 🎯 Quick Commands

### First Time Setup
```bash
# Install dependencies (run once)
python install_dependencies.py
```

### Daily Usage

#### On Your Windows PC:
```bash
# Train model
python run.py --train

# Optimize hyperparameters (your main question)
python run.py --optimize --n_trials 25

# Evaluate model
python run.py --eval_only

# Generate signals
python run.py --signal
```

#### On Google Colab:
```python
# Train model
!python run.py --train

# Optimize hyperparameters
!python run.py --optimize --n_trials 25

# Evaluate model
!python run.py --eval_only

# Generate signals
!python run.py --signal
```

## 📁 Files You Need (Only 3!)

1. **`SPY-Aroon UP.py`** - Your main trading script
2. **`install_dependencies.py`** - Installs packages (run once)
3. **`run.py`** - Simple launcher (handles the filename automatically)

## 🔧 That's It!

- **CPU mode by default** (no more switching)
- **Auto-detects your environment** (PC vs Colab)
- **Handles the filename** (no more quotes around "SPY-Aroon UP.py")
- **Same commands everywhere**

Your original failing command:
```
--optimize--n_trials 25_TRIALS  ❌
```

Now becomes:
```bash
python run.py --optimize --n_trials 25  ✅
```